{"$schema": "http://json-schema.org/draft-07/schema#", "title": "Video Quiz Configuration <PERSON><PERSON><PERSON>", "type": "object", "required": ["videoConfig", "quizSchedule", "quizzes"], "properties": {"$schema": {"type": "string", "description": "Schema reference"}, "videoConfig": {"type": "object", "required": ["source"], "properties": {"source": {"type": "string"}, "startOffset": {"type": "number", "default": 0}, "endPadding": {"type": "number", "default": 5}}}, "quizSchedule": {"type": "array", "items": {"type": "object", "required": ["time", "quizId"], "properties": {"time": {"type": "number"}, "quizId": {"type": "string"}, "title": {"type": "string"}, "description": {"type": "string"}}}}, "quizzes": {"type": "object", "patternProperties": {"^\\d+$": {"type": "object", "required": ["title", "questions"], "properties": {"title": {"type": "string"}, "description": {"type": "string"}, "present_items": {"type": ["number", "string"]}, "timeLimit": {"type": "number"}, "questions": {"type": "array", "items": {"type": "object", "required": ["id", "question", "type"], "oneOf": [{"properties": {"type": {"const": "multipleChoice"}, "options": {"type": "array"}, "correctAnswer": {"type": "string"}}}, {"properties": {"type": {"const": "trueFalse"}, "correctAnswer": {"type": "boolean"}}}, {"properties": {"type": {"const": "shortAnswer"}, "validation": {"type": "object", "properties": {"required": {"type": "boolean"}, "minLength": {"type": "number"}, "maxLength": {"type": "number"}}}}}, {"properties": {"type": {"const": "imageRate"}, "options": {"type": "array", "description": "First element contains the image path"}, "validation": {"type": "object", "properties": {"required": {"type": "boolean"}, "minLength": {"type": "number"}, "maxLength": {"type": "number"}}}}}, {"properties": {"type": {"const": "sliderRating"}, "options": {"type": "object", "properties": {"leftValue": {"type": "number"}, "rightValue": {"type": "number"}, "leftLabel": {"type": "string"}, "rightLabel": {"type": "string"}, "sliderText": {"type": "string"}}, "required": ["leftValue", "rightValue", "leftLabel", "<PERSON><PERSON><PERSON><PERSON>"]}}}]}}}}}}}}