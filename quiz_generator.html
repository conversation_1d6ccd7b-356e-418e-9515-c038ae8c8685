<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <title>Quiz Generator</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500&display=swap">
    <style>
        .quiz-section { margin: 20px 0; padding: 15px; border: 1px solid #ccc; }
        .question-section { margin: 10px 0; padding: 10px; background: #f5f5f5; }
        .hidden { display: none; }
        body {
            font-family: 'Poppins', sans-serif;
            background: #f8f9fa;
            margin: 0;
            padding: 20px;
        }
        form {
            max-width: 800px;
            margin: 0 auto;
            background: #ffffff;
            padding: 20px;
            border-radius: 8px;
        }
        form > div {
            margin-bottom: 15px;
        }
        form button {
            margin-top: 10px;
            cursor: pointer;
        }
        h3 {
            margin-top: 0;
        }
        .quiz-section, .upload-section {
            margin-top: 20px;
            background: #fafafa;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 15px;
        }
        .quiz-section:hover, .upload-section:hover {
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            transform: translateY(-2px);
            transition: 0.2s ease-in-out;
        }
        .question-section:hover {
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            transition: 0.2s ease-in-out;
        }
    </style>
</head>
<body>
    <form id="quizGeneratorForm">
        <div>
            <label for="videoSource">Video Source:</label>
            <div>
                <input type="radio" name="videoSourceType" id="filePathSource" value="filePath" checked>
                <label for="filePathSource">File Path</label>
                
                <input type="radio" name="videoSourceType" id="youtubeSource" value="youtube">
                <label for="youtubeSource">YouTube URL</label>
            </div>
            <input type="text" id="videoSource" placeholder="Enter file path or YouTube URL" required>
            <div id="videoSourceHelp" class="form-help">Enter the path to your video file (e.g., videos/lecture.mp4)</div>
        </div>

        <div>
            <button type="button" id="uploadIdsBtn">Upload IDs?</button>
            <input type="file" id="idFile" accept=".txt,.csv" style="display: none;">
            <span id="uploadStatus"></span>
        </div>

        <div>
            <label>
            <input type="checkbox" id="togglePhpResults"> Save Results to Server (PHP)?
            </label>
        </div>
        
        <div>
            <label for="quizCount">Number of Quizzes:</label>
            <input type="number" id="quizCount" min="1" required>
            <button type="button" id="generateQuizFields">Generate Quiz Fields</button>
        </div>

        <div id="quizzesContainer"></div>

        <div>
            <label for="outputFileName">Output Filename Prefix:</label>
            <input type="text" id="outputFileName" placeholder="e.g., lecture1" value="generated_video_quiz" required>
            <div class="form-help">Files will be generated as [prefix].html and [prefix].js</div>
        </div>
        
        <button type="submit">Generate Files</button>
    </form>

    <div>
        <label for="exportFileName">Export JSON filename:</label>
        <input type="text" id="exportFileName" placeholder="e.g. my_question_bank.json" style="width: 50%;">
        <button type="button" id="exportJsonBtn">Export JSON</button>
    </div>

    <div id="outputContainer"></div>
    
    <script src="quiz_generator.js"></script>
</body>
</html>
